/**
 * FILE: ItForceCore.swift
 *
 * DESCRIPTION:
 *     Main module entry point for ItForce VPN Core library
 *     Provides unified access to all core functionality
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

// MARK: - Infrastructure Layer Exports
@_exported import Foundation
@_exported import NetworkExtension
@_exported import OSLog
@_exported import CryptoKit

// MARK: - Module Version Information
public struct ItForceCoreVersion {
    public static let major = 1
    public static let minor = 0
    public static let patch = 0
    public static let version = "\(major).\(minor).\(patch)"
    public static let buildDate = "2025-06-23"
}

// MARK: - Core Module Initialization
/**
 * NAME: ItForceCoreManager
 *
 * DESCRIPTION:
 *     Central manager for ItForce VPN Core library initialization and configuration
 *
 * PROPERTIES:
 *     shared - Singleton instance
 *     isInitialized - Initialization status
 *
 * METHODS:
 *     initialize - Initialize the core library
 *     shutdown - Shutdown and cleanup resources
 */
public final class ItForceCoreManager {
    public static let shared = ItForceCoreManager()
    
    private var _isInitialized = false
    public var isInitialized: Bool { _isInitialized }
    
    private init() {}
    
    /**
     * NAME: initialize
     *
     * DESCRIPTION:
     *     Initialize the ItForce VPN Core library with configuration
     *
     * PARAMETERS:
     *     configuration - Core library configuration
     *
     * THROWS:
     *     CoreInitializationError - If initialization fails
     */
    public func initialize(configuration: CoreConfiguration = .default) throws {
        guard !_isInitialized else {
            throw CoreInitializationError.alreadyInitialized
        }
        
        // Initialize logging system
        try LoggingManager.shared.initialize(configuration: configuration.logging)
        
        // Initialize performance monitoring
        try PerformanceManager.shared.initialize(configuration: configuration.performance)
        
        // Initialize error handling
        ErrorManager.shared.initialize(configuration: configuration.errorHandling)
        
        _isInitialized = true
        
        LoggingManager.shared.logger.logInfo("ItForce VPN Core initialized successfully",
            fields: LogField.string("version", ItForceCoreVersion.version),
            LogField.string("build", ItForceCoreVersion.buildDate)
        )
    }
    
    /**
     * NAME: shutdown
     *
     * DESCRIPTION:
     *     Shutdown the core library and cleanup all resources
     */
    public func shutdown() async {
        guard _isInitialized else { return }
        
        LoggingManager.shared.logger.logInfo("Shutting down ItForce VPN Core")

        // Shutdown in reverse order
        await PerformanceManager.shared.shutdown()
        
        _isInitialized = false
    }
}

// MARK: - Core Configuration
/**
 * NAME: CoreConfiguration
 *
 * DESCRIPTION:
 *     Configuration structure for ItForce VPN Core library
 *
 * PROPERTIES:
 *     logging - Logging system configuration
 *     performance - Performance monitoring configuration
 *     errorHandling - Error handling configuration
 */
public struct CoreConfiguration {
    public let logging: LoggingConfiguration
    public let performance: PerformanceConfiguration
    public let errorHandling: ErrorHandlingConfiguration

    public init(
        logging: LoggingConfiguration = .production,
        performance: PerformanceConfiguration = .default,
        errorHandling: ErrorHandlingConfiguration = .default
    ) {
        self.logging = logging
        self.performance = performance
        self.errorHandling = errorHandling
    }

    public static let `default` = CoreConfiguration()
}

// MARK: - Core Initialization Errors
/**
 * NAME: CoreInitializationError
 *
 * DESCRIPTION:
 *     Errors that can occur during core library initialization
 *
 * CASES:
 *     alreadyInitialized - Library is already initialized
 *     configurationInvalid - Invalid configuration provided
 *     systemResourceUnavailable - Required system resources unavailable
 */
public enum CoreInitializationError: Error, LocalizedError {
    case alreadyInitialized
    case configurationInvalid(String)
    case systemResourceUnavailable(String)
    
    public var errorDescription: String? {
        switch self {
        case .alreadyInitialized:
            return "Panabit Client VPN Core is already initialized"
        case .configurationInvalid(let details):
            return "Invalid configuration: \(details)"
        case .systemResourceUnavailable(let resource):
            return "System resource unavailable: \(resource)"
        }
    }
}

// MARK: - Convenience Access
/**
 * NAME: Core
 *
 * DESCRIPTION:
 *     Convenience namespace for accessing core functionality
 */
public enum Core {
    public static var manager: ItForceCoreManager { ItForceCoreManager.shared }
    public static var logger: Logger { LoggingManager.shared.logger }
    public static var performance: PerformanceManager { PerformanceManager.shared }
    public static var version: String { ItForceCoreVersion.version }
}
