// /*******************************************************************************
//  * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
//  *
//  * This source code is confidential, proprietary, and contains trade
//  * secrets that are the sole property of UNISASE Corporation.
//  * Copy and/or distribution of this source code or disassembly or reverse
//  * engineering of the resultant object code are strictly forbidden without
//  * the written consent of UNISASE Corporation LLC.
//  *
//  *******************************************************************************
//  * FILE NAME :      constants.dart
//  *
//  * DESCRIPTION :    应用程序全局常量定义文件，包含API配置、颜色主题、
//  *                  状态枚举、WebSocket事件类型等核心常量
//  *
//  * AUTHOR :         wei
//  *
//  * HISTORY :        10/06/2025 create
//  ******************************************************************************/



/// 应用程序常量定义
///
/// PURPOSE:
///     定义应用程序中使用的所有常量，包括API配置、颜色主题、
///     状态枚举、WebSocket事件类型等，确保常量的统一管理和维护
///
/// FEATURES:
///     - API和WebSocket连接配置
///     - 本地存储键名定义
///     - 许可协议URL配置
///     - 应用颜色主题定义
///     - 连接状态和日志级别枚举
///     - WebSocket事件类型常量
///     - 后端服务配置参数
///
/// USAGE:
///     import 'package:panabit_client/utils/constants.dart';
///
///     // 使用API配置
///     final apiUrl = kApiBaseUrl;
///
///     // 使用颜色常量
///     final primaryColor = AppColors.primary;
///
///     // 使用状态枚举
///     final status = ConnectionStatus.connected;

// =============================================================================
// API和网络配置常量
// =============================================================================

/// API基础URL
///
/// DESCRIPTION:
///     后端API服务的基础URL地址，用于所有HTTP请求
const String kApiBaseUrl = 'http://localhost:56543/api';

/// WebSocket连接URL
///
/// DESCRIPTION:
///     WebSocket服务的连接地址，用于实时通信
const String kWebSocketUrl = 'ws://localhost:56544/ws';

// =============================================================================
// 本地存储键名常量
// =============================================================================

/// 用户认证相关存储键
///
/// DESCRIPTION:
///     用于SharedPreferences存储用户认证信息的键名
class StorageKeys {
  /// 用户名存储键
  static const String username = 'username';

  /// 密码存储键
  static const String password = 'password';

  /// 域名存储键
  static const String domain = 'domain';

  /// 记住凭据选项存储键
  static const String rememberCredentials = 'remember_credentials';

  /// 主题设置存储键
  static const String theme = 'theme';

  /// 语言设置存储键
  static const String language = 'language';

  /// 用户信息存储键
  static const String userInfo = 'user_info';

  /// 许可协议接受状态存储键
  static const String acceptLicense = 'accept_license';

  /// 缓存的接口信息存储键
  static const String cachedInterfaceInfo = 'cached_interface_info';
}

// =============================================================================
// 许可协议URL常量
// =============================================================================

/// 许可协议URL配置
///
/// DESCRIPTION:
///     应用程序许可协议和隐私政策的URL地址配置
class LicenseUrls {
  /// 中文服务条款URL
  static const String termsServiceZh = 'https://www.panabit.com/term_service_zh';

  /// 英文服务条款URL
  static const String termsServiceEn = 'https://www.panabit.com/term_service_en';

  /// 中文隐私政策URL
  static const String privacyPolicyZh = 'https://www.panabit.com/privacy_policy_zh';

  /// 英文隐私政策URL
  static const String privacyPolicyEn = 'https://www.panabit.com/privacy_policy_en';
}

// =============================================================================
// 注意：颜色定义已迁移到 design_system.dart 中的 AppColors 类
// 请使用新的设计系统颜色定义
// =============================================================================

// =============================================================================
// 枚举类型定义
// =============================================================================

/// 连接状态枚举
///
/// PURPOSE:
///     定义VPN连接的各种状态，用于状态管理和UI显示
///
/// VALUES:
///     disconnected - 已断开连接
///     connecting - 正在连接
///     connected - 已连接
///     disconnecting - 正在断开连接
///     error - 连接错误
enum ConnectionStatus {
  disconnected,
  connecting,
  connected,
  disconnecting,
  error,
}

/// 日志级别枚举
///
/// PURPOSE:
///     定义日志记录的级别，用于日志过滤和显示
///
/// VALUES:
///     debug - 调试信息
///     info - 一般信息
///     warning - 警告信息
///     error - 错误信息
enum LogLevel {
  debug,
  info,
  warning,
  error,
}

// =============================================================================
// WebSocket事件类型常量
// =============================================================================

/// WebSocket事件类型定义
///
/// PURPOSE:
///     定义WebSocket通信中使用的事件类型常量
class WebSocketEvents {
  /// 状态更新事件
  static const String status = 'status';

  /// 错误事件
  static const String error = 'error';

  /// 服务器列表事件
  static const String servers = 'servers';

  /// 心跳事件
  static const String heartbeat = 'heartbeat';

  /// Ping开始事件
  static const String pingStart = 'ping_start';

  /// Ping完成事件
  static const String pingComplete = 'ping_complete';

  /// Ping结果事件
  static const String pingResults = 'ping_results';

  /// 连接服务器事件
  static const String connServer = 'conn_server';

  /// 流量统计事件
  static const String traffic = 'traffic';

  /// 通知事件
  static const String notification = 'notification';

  /// 接口信息事件
  static const String interfaceInfo = 'interface_info';

  /// 后端关闭启动事件
  static const String shutdownInitiated = 'shutdown_initiated';

  /// 后端关闭完成事件
  static const String shutdownComplete = 'shutdown_complete';

  /// 需要重连事件
  static const String reconnectRequired = 'reconnect_required';
}

// =============================================================================
// 连接状态字符串常量
// =============================================================================

/// 连接状态字符串定义
///
/// PURPOSE:
///     定义连接状态的字符串表示，用于API通信和状态比较
class ConnectionStatusStrings {
  /// 已断开连接状态
  static const String disconnected = 'disconnected';

  /// 正在连接状态
  static const String connecting = 'connecting';

  /// 已连接状态
  static const String connected = 'connected';

  /// 正在断开连接状态
  static const String disconnecting = 'disconnecting';

  /// 错误状态
  static const String error = 'error';
}

// =============================================================================
// 状态消息常量
// =============================================================================

/// 状态消息常量定义
///
/// PURPOSE:
///     定义应用程序中使用的状态消息常量
class StatusMessages {
  /// 断开连接消息（小写）
  static const String disconnected = 'disconnected';

  /// 断开连接消息（首字母大写）
  static const String disconnectedCapital = 'Disconnected';

  /// 默认服务器名称
  static const String defaultServerName = 'None';
}

// =============================================================================
// 日志模块常量
// =============================================================================

/// 日志模块名称定义
///
/// PURPOSE:
///     定义日志记录中使用的模块名称常量
class LogModules {
  /// 应用状态模块
  static const String appState = 'AppState';
}

// =============================================================================
// 应用版本和配置常量
// =============================================================================

/// 应用程序版本信息
///
/// DESCRIPTION:
///     应用程序的基础版本号，构建时会添加Git提交信息
const String kAppVersion = '1.0.0';

/// 功能版本控制
///
/// PURPOSE:
///     控制应用程序功能的显示，支持不同版本的功能差异化
class FeatureVersion {
  /// 是否显示个人信息部分（0: 隐藏, 1: 显示）
  static const int showPersonalInfo = 0;
}

/// 后端服务配置
///
/// PURPOSE:
///     定义后端服务的路径和端口配置
class BackendConfig {
  /// 后端服务可执行文件路径
  static const String servicePath = 'panabit-service.exe';

  /// 后端服务端口（用于健康检查）
  static const int servicePort = 56543;
}

/// 系统托盘配置
///
/// PURPOSE:
///     定义系统托盘相关的配置选项
class SystemTrayConfig {
  /// 关闭窗口时是否最小化到系统托盘
  static const bool minimizeToTrayOnClose = true;
}

// =============================================================================
// 窗口配置常量
// =============================================================================

/// 应用程序标题
///
/// DESCRIPTION:
///     应用程序窗口标题
const String kAppTitle = 'Panabit iWAN';

/// 窗口宽度
///
/// DESCRIPTION:
///     应用程序窗口的固定宽度（像素）
const double kWindowWidth = 590.0;

/// 窗口高度
///
/// DESCRIPTION:
///     应用程序窗口的固定高度（像素）
const double kWindowHeight = 844.0;

/// 窗口模块日志标识
///
/// DESCRIPTION:
///     窗口服务日志记录的模块标识
const String kLogModuleWindow = 'Window';

// =============================================================================
// 应用程序主模块常量
// =============================================================================

/// 应用程序主模块日志标识
///
/// DESCRIPTION:
///     应用程序主模块日志记录的模块标识
const String kLogModuleApp = 'App';

/// 应用程序启动成功消息
///
/// DESCRIPTION:
///     应用程序成功启动时的日志消息
const String kMessageAppStarted = 'MyApp started';

/// 应用程序初始化失败错误消息
///
/// DESCRIPTION:
///     应用程序初始化失败时的错误消息
const String kErrorAppInitializationFailed = 'Failed to initialize app';

/// 应用程序初始化失败退出码
///
/// DESCRIPTION:
///     应用程序初始化失败时的进程退出码
const int kExitCodeInitializationFailed = 1;

/// 错误处理器模块日志标识
///
/// DESCRIPTION:
///     错误处理器日志记录的模块标识
const String kLogModuleErrorHandler = 'ErrorHandler';


