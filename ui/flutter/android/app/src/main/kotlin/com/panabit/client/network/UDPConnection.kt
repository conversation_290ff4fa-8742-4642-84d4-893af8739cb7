/**
 * FILE: UDPConnection.kt
 *
 * DESCRIPTION:
 *     UDP connection management for SDWAN protocol communication.
 *     Provides reliable UDP connection with timeout handling and error recovery.
 *     Based on Go backend net.DialUDP and iOS NWConnection implementations.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.network

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError
import com.panabit.client.vpn.ITforceVPNService

import kotlinx.coroutines.*
import java.io.IOException
import java.net.*
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * NAME: UDPConnection
 *
 * DESCRIPTION:
 *     UDP connection implementation using DatagramSocket for SDWAN protocol.
 *     Provides asynchronous send/receive operations with timeout handling.
 *     Thread-safe implementation using Kotlin coroutines and atomic operations.
 *
 * PROPERTIES:
 *     serverAddress - Target server IP address
 *     serverPort - Target server port
 *     socket - UDP socket for communication
 *     isConnected - Connection state flag
 *     bytesSent - Total bytes sent counter
 *     bytesReceived - Total bytes received counter
 */
class UDPConnection(
    val serverAddress: InetAddress,
    private val serverPort: Int
) {
    companion object {
        private const val DEFAULT_TIMEOUT_MS = 5000L
        private const val RECEIVE_BUFFER_SIZE = 65536
        private const val SEND_TIMEOUT_MS = 3000L
        private const val RECEIVE_TIMEOUT_MS = 1000L
    }    private var _socket: DatagramSocket? = null
    private val connectionState = AtomicBoolean(false)
    private val bytesSent = AtomicLong(0)
    private val bytesReceived = AtomicLong(0)

    /**
     * NAME: socket
     *
     * DESCRIPTION:
     *     Public getter for the underlying DatagramSocket.
     *     Required for HeartbeatManager integration.
     *
     * RETURNS:
     *     DatagramSocket - The underlying socket, or throws exception if not connected
     */
    val socket: DatagramSocket
        get() = _socket ?: throw IllegalStateException("UDP connection not established")

    // Connection monitoring
    private val lastSendTime = AtomicLong(0)
    private val lastReceiveTime = AtomicLong(0)

    /**
     * NAME: connect
     *
     * DESCRIPTION:
     *     Establishes UDP connection to server.
     *     Creates DatagramSocket and validates connectivity.
     *
     * RETURNS:
     *     Result<Unit> - Success or failure result
     */
    suspend fun connect(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            logInfo("Connecting to UDP server", mapOf(
                "server_address" to serverAddress.hostAddress,
                "server_port" to serverPort
            ))

            // Close existing connection if any
            close()

            // Create new DatagramSocket
            val newSocket = DatagramSocket()

            // Note: Socket protection will be applied after VPN interface is created
            // to ensure proper timing (protect() only works after VPN is established)

            // Configure socket options for optimal performance
            newSocket.soTimeout = RECEIVE_TIMEOUT_MS.toInt()
            newSocket.sendBufferSize = RECEIVE_BUFFER_SIZE
            newSocket.receiveBufferSize = RECEIVE_BUFFER_SIZE
            newSocket.reuseAddress = true

            // Set socket and update state atomically
            _socket = newSocket
            connectionState.set(true)

            logInfo("UDP connection established successfully", mapOf(
                "local_port" to newSocket.localPort,
                "server_endpoint" to "${serverAddress.hostAddress}:$serverPort"
            ))

            Result.success(Unit)

        } catch (e: Exception) {
            logError("Failed to establish UDP connection", mapOf(
                "error" to (e.message ?: "unknown"),
                "server_address" to serverAddress.hostAddress,
                "server_port" to serverPort
            ), e)

            close()
            Result.failure(e)
        }
    }

    /**
     * NAME: send
     *
     * DESCRIPTION:
     *     Sends data through UDP connection.
     *     Thread-safe operation with timeout handling.
     *
     * PARAMETERS:
     *     data - Data to send
     *
     * RETURNS:
     *     Result<Unit> - Success or failure result
     */
    suspend fun send(data: ByteArray): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val currentSocket = _socket
            if (currentSocket == null || !connectionState.get()) {
                return@withContext Result.failure(IOException("Connection not established"))
            }

            if (data.isEmpty()) {
                return@withContext Result.success(Unit)
            }

            // logDebug("Sending UDP packet", mapOf(
            //     "packet_size" to data.size,
            //     "server_endpoint" to "${serverAddress.hostAddress}:$serverPort",
            //     "socket_local_port" to currentSocket.localPort,
            //     "socket_id" to currentSocket.hashCode()
            // ))

            val packet = DatagramPacket(data, data.size, serverAddress, serverPort)

            // Send with timeout and measure duration
            // val sendStartTime = System.nanoTime()
            withTimeout(SEND_TIMEOUT_MS) {
                currentSocket.send(packet)
            }
            // val sendEndTime = System.nanoTime()
            // val sendDurationMs = (sendEndTime - sendStartTime) / 1_000_000

            // Update statistics
            val totalBytesSent = bytesSent.addAndGet(data.size.toLong())
            lastSendTime.set(System.currentTimeMillis())

            // logDebug("UDP packet sent successfully", mapOf(
            //     "bytes_sent" to data.size,
            //     "total_bytes_sent" to totalBytesSent,
            //     "send_duration_ms" to sendDurationMs
            // ))

            // 记录大包或异常情况
            // if (data.size > 1400) {
            //     logWarn("Large UDP packet sent", mapOf(
            //         "packet_size" to data.size,
            //         "max_expected_size" to 1400,
            //         "may_fragment" to true
            //     ))
            // }

            // if (sendDurationMs > 10) {
            //     logWarn("Slow UDP send detected", mapOf(
            //         "send_duration_ms" to sendDurationMs,
            //         "packet_size" to data.size
            //     ))
            // }

            Result.success(Unit)

        } catch (e: Exception) {
            logError("Failed to send UDP packet", mapOf(
                "error" to (e.message ?: "unknown"),
                "packet_size" to data.size,
                "error_type" to e::class.java.simpleName
            ), e)

            // Check if this is a network error that should trigger reconnection
            val errorMessage = e.message ?: ""
            val isNetworkError = when {
                errorMessage.contains("ENETUNREACH") -> true
                errorMessage.contains("Network is unreachable") -> true
                errorMessage.contains("EHOSTUNREACH") -> true
                errorMessage.contains("Host is unreachable") -> true
                errorMessage.contains("ENETDOWN") -> true
                errorMessage.contains("Network is down") -> true
                errorMessage.contains("ECONNREFUSED") -> true
                errorMessage.contains("Connection refused") -> true
                errorMessage.contains("EHOSTDOWN") -> true
                errorMessage.contains("Host is down") -> true
                else -> false
            }

            if (isNetworkError) {
                logWarn("Network error detected in UDP send", mapOf(
                    "error_message" to errorMessage,
                    "should_trigger_reconnect" to true
                ))
            }

            Result.failure(e)
        }
    }

    /**
     * NAME: receive
     *
     * DESCRIPTION:
     *     Receives data from UDP connection.
     *     Non-blocking operation with timeout handling.
     *
     * RETURNS:
     *     Result<ByteArray> - Received data or failure result
     */
    suspend fun receive(): Result<ByteArray> = withContext(Dispatchers.IO) {
        try {
            val currentSocket = _socket
            if (currentSocket == null || !connectionState.get()) {
                return@withContext Result.failure(IOException("Connection not established"))
            }

            // Additional check for socket state before attempting receive
            if (currentSocket.isClosed) {
                return@withContext Result.failure(IOException("Socket is closed"))
            }

            val buffer = ByteArray(RECEIVE_BUFFER_SIZE)
            val packet = DatagramPacket(buffer, buffer.size)

            try {
                // 🚀 PERFORMANCE FIX: Use runInterruptible to make blocking receive cancellable
                // This allows the coroutine to respond to cancellation immediately
                // val receiveStartTime = System.nanoTime()
                runInterruptible {
                    currentSocket.receive(packet)
                }
                // val receiveEndTime = System.nanoTime()
                // val receiveDurationMs = (receiveEndTime - receiveStartTime) / 1_000_000

                val receivedData = packet.data.copyOfRange(0, packet.length)

                // Update statistics
                val totalBytesReceived = bytesReceived.addAndGet(receivedData.size.toLong())
                lastReceiveTime.set(System.currentTimeMillis())

                // logDebug("UDP packet received", mapOf(
                //     "bytes_received" to receivedData.size,
                //     "total_bytes_received" to totalBytesReceived,
                //     "source_address" to (packet.address?.hostAddress ?: "unknown"),
                //     "receive_duration_ms" to receiveDurationMs,
                //     "buffer_utilization_percent" to ((receivedData.size * 100) / RECEIVE_BUFFER_SIZE)
                // ))

                // 记录大包或异常情况
                // if (receivedData.size > 1400) {
                //     logWarn("Large UDP packet received", mapOf(
                //         "packet_size" to receivedData.size,
                //         "max_expected_size" to 1400
                //     ))
                // }

                Result.success(receivedData)

            } catch (e: SocketTimeoutException) {
                // Timeout is expected for non-blocking operation
                Result.failure(e)
            } catch (e: SocketException) {
                // Handle socket closure during receive operation
                if (e.message?.contains("Bad file descriptor") == true ||
                    e.message?.contains("EBADF") == true ||
                    !connectionState.get() ||
                    currentSocket.isClosed) {
                    // Socket was closed during operation - this is expected during shutdown
                    return@withContext Result.failure(IOException("Socket closed during receive operation"))
                }
                // Re-throw other socket exceptions
                throw e
            }

        } catch (e: Exception) {
            logError("Failed to receive UDP packet", mapOf(
                "error" to (e.message ?: "unknown"),
                "connection_state" to connectionState.get(),
                "socket_available" to (_socket != null),
                "socket_closed" to (_socket?.isClosed ?: true),
                "error_type" to e::class.java.simpleName
            ), e)

            // Check if this is a network error that should trigger reconnection
            val errorMessage = e.message ?: ""
            val isNetworkError = when {
                errorMessage.contains("ENETUNREACH") -> true
                errorMessage.contains("Network is unreachable") -> true
                errorMessage.contains("EHOSTUNREACH") -> true
                errorMessage.contains("Host is unreachable") -> true
                errorMessage.contains("ENETDOWN") -> true
                errorMessage.contains("Network is down") -> true
                errorMessage.contains("ECONNREFUSED") -> true
                errorMessage.contains("Connection refused") -> true
                errorMessage.contains("EHOSTDOWN") -> true
                errorMessage.contains("Host is down") -> true
                else -> false
            }

            if (isNetworkError) {
                logWarn("Network error detected in UDP receive", mapOf(
                    "error_message" to errorMessage,
                    "should_trigger_reconnect" to true
                ))
            }

            Result.failure(e)
        }
    }

    /**
     * NAME: protectSocket
     *
     * DESCRIPTION:
     *     Protects the UDP socket from VPN routing after VPN interface is created.
     *     Must be called after VPN establishment to prevent routing loops.
     *
     * RETURNS:
     *     Boolean - true if protection was successful, false otherwise
     */
    fun protectSocket(): Boolean {
        val currentSocket = _socket
        if (currentSocket == null) {
            logWarn("Cannot protect socket - no active socket")
            return false
        }

        val vpnService = ITforceVPNService.getInstance()
        if (vpnService == null) {
            logWarn("Cannot protect socket - VPN service not available")
            return false
        }

        val protected = vpnService.protectUDPSocket(currentSocket)
        logInfo("UDP socket protection applied", mapOf(
            "protected" to protected,
            "server_address" to serverAddress.hostAddress,
            "local_port" to currentSocket.localPort
        ))
        return protected
    }

    /**
     * NAME: close
     *
     * DESCRIPTION:
     *     Closes UDP connection and releases resources.
     *     Idempotent operation - safe to call multiple times.
     */
    suspend fun close() = withContext(Dispatchers.IO) {
        try {
            connectionState.set(false)
            _socket?.close()
            _socket = null

            // Reset statistics
            bytesSent.set(0)
            bytesReceived.set(0)
            lastSendTime.set(0)
            lastReceiveTime.set(0)

            logInfo("UDP connection closed")

        } catch (e: Exception) {
            logError("Error closing UDP connection", mapOf(
                "error" to (e.message ?: "unknown")
            ), e)
        }
    }

    /**
     * NAME: isConnected
     *
     * DESCRIPTION:
     *     Checks if UDP connection is established.
     *
     * RETURNS:
     *     Boolean - true if connected, false otherwise
     */
    fun isConnected(): Boolean {
        val currentSocket = _socket
        return connectionState.get() && currentSocket != null && !currentSocket.isClosed
    }

    /**
     * NAME: getConnectionStats
     *
     * DESCRIPTION:
     *     Returns connection statistics.
     *
     * RETURNS:
     *     Map<String, Any> - Connection statistics
     */
    fun getConnectionStats(): Map<String, Any> {
        val currentSocket = _socket
        return mapOf(
            "is_connected" to isConnected(),
            "bytes_sent" to bytesSent.get(),
            "bytes_received" to bytesReceived.get(),
            "last_send_time" to lastSendTime.get(),
            "last_receive_time" to lastReceiveTime.get(),
            "server_address" to serverAddress.hostAddress,
            "server_port" to serverPort,
            "local_port" to (currentSocket?.localPort ?: 0)
        )
    }


}
