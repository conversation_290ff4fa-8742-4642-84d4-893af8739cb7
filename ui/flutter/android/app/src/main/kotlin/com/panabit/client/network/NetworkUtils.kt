/**
 * FILE: NetworkUtils.kt
 *
 * DESCRIPTION:
 *     Network utility functions for address resolution, timeout handling, and retry mechanisms.
 *     Provides common network operations used across the application.
 *     Based on Go backend network utilities and iOS network helper functions.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

package com.panabit.client.network

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import kotlinx.coroutines.*
import java.net.*

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.LinkProperties

/**
 * NAME: NetworkUtils
 *
 * DESCRIPTION:
 *     Utility object providing network-related helper functions.
 *     Contains DNS resolution, timeout handling, and retry mechanisms.
 *
 * METHODS:
 *     resolveAddress - Resolves hostname to IP address
 *     resolveAddressWithTimeout - Resolves address with timeout
 *     performWithRetry - Executes operation with retry logic
 *     validateIPAddress - Validates IP address format
 *     isNetworkAvailable - Checks network availability
 */
object NetworkUtils {    
    // DNS resolution constants
    private const val DNS_TIMEOUT_MS = 5000L
    private const val MAX_RETRY_ATTEMPTS = 3
    private const val RETRY_DELAY_MS = 1000L

    /**
     * NAME: resolveAddress
     *
     * DESCRIPTION:
     *     Resolves hostname to IP address using DNS lookup.
     *     Returns first IPv4 address found for the hostname.
     *
     * PARAMETERS:
     *     hostname - Hostname to resolve
     *
     * RETURNS:
     *     Result<InetAddress> - Resolved IP address or failure result
     */
    suspend fun resolveAddress(hostname: String): Result<InetAddress> = withContext(Dispatchers.IO) {
        try {
            logInfo("Resolving hostname", mapOf("hostname" to hostname))

            // Check if hostname is already an IP address
            if (isValidIPAddress(hostname)) {
                val address = InetAddress.getByName(hostname)
                logInfo("Hostname is already IP address", mapOf(
                    "hostname" to hostname,
                    "ip_address" to address.hostAddress
                ))
                return@withContext Result.success(address)
            }

            // Perform DNS resolution with timeout
            val result = resolveAddressWithTimeout(hostname, DNS_TIMEOUT_MS)
            
            result.onSuccess { address ->
                logInfo("DNS resolution successful", mapOf(
                    "hostname" to hostname,
                    "resolved_ip" to address.hostAddress
                ))
            }.onFailure { error ->
                logError("DNS resolution failed", mapOf(
                    "hostname" to hostname,
                    "error" to (error.message ?: "unknown")
                ), error)
            }

            result

        } catch (e: Exception) {
            logError("DNS resolution error", mapOf(
                "hostname" to hostname,
                "error" to (e.message ?: "unknown")
            ), e)
            Result.failure(e)
        }
    }

    /**
     * NAME: resolveAddressWithTimeout
     *
     * DESCRIPTION:
     *     Resolves hostname with specified timeout.
     *     Uses coroutine timeout to prevent blocking operations.
     *
     * PARAMETERS:
     *     hostname - Hostname to resolve
     *     timeoutMs - Timeout in milliseconds
     *
     * RETURNS:
     *     Result<InetAddress> - Resolved address or timeout failure
     */
    private suspend fun resolveAddressWithTimeout(
        hostname: String, 
        timeoutMs: Long
    ): Result<InetAddress> {
        return try {
            withTimeout(timeoutMs) {
                val addresses = InetAddress.getAllByName(hostname)
                
                // Find first IPv4 address
                val ipv4Address = addresses.firstOrNull { it is Inet4Address }
                    ?: addresses.firstOrNull()
                
                if (ipv4Address != null) {
                    Result.success(ipv4Address)
                } else {
                    Result.failure(UnknownHostException("No IPv4 address found for $hostname"))
                }
            }
        } catch (e: TimeoutCancellationException) {
            Result.failure(SocketTimeoutException("DNS resolution timeout for $hostname"))
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * NAME: performWithRetry
     *
     * DESCRIPTION:
     *     Executes operation with retry logic and exponential backoff.
     *     Retries failed operations up to specified maximum attempts.
     *
     * PARAMETERS:
     *     maxAttempts - Maximum number of retry attempts
     *     initialDelayMs - Initial delay between retries
     *     operation - Operation to execute
     *
     * RETURNS:
     *     Result<T> - Operation result or final failure
     */
    suspend fun <T> performWithRetry(
        maxAttempts: Int = MAX_RETRY_ATTEMPTS,
        initialDelayMs: Long = RETRY_DELAY_MS,
        operation: suspend (attempt: Int) -> Result<T>
    ): Result<T> {
        var lastException: Exception? = null
        var delayMs = initialDelayMs

        repeat(maxAttempts) { attempt ->
            try {
                val result = operation(attempt + 1)
                if (result.isSuccess) {
                    return result
                }
                
                lastException = result.exceptionOrNull() as? Exception
                    ?: Exception("Operation failed on attempt ${attempt + 1}")

            } catch (e: Exception) {
                lastException = e
            }

            // Don't delay after the last attempt
            if (attempt < maxAttempts - 1) {
                logDebug("Retrying operation", mapOf(
                    "attempt" to (attempt + 1),
                    "max_attempts" to maxAttempts,
                    "delay_ms" to delayMs
                ))
                
                delay(delayMs)
                delayMs = (delayMs * 1.5).toLong() // Exponential backoff
            }
        }

        return Result.failure(lastException ?: Exception("All retry attempts failed"))
    }

    /**
     * NAME: isValidIPAddress
     *
     * DESCRIPTION:
     *     Validates if string is a valid IP address.
     *     Supports both IPv4 and IPv6 formats.
     *
     * PARAMETERS:
     *     address - Address string to validate
     *
     * RETURNS:
     *     Boolean - true if valid IP address, false otherwise
     */
    fun isValidIPAddress(address: String): Boolean {
        return try {
            InetAddress.getByName(address)
            // Additional check to ensure it's actually an IP address, not a hostname
            address.matches(Regex("^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$")) ||
            address.contains(":") // Simple IPv6 check
        } catch (e: Exception) {
            false
        }
    }

    /**
     * NAME: validateIPAddress
     *
     * DESCRIPTION:
     *     Validates IP address format and returns detailed result.
     *     Provides more information than simple boolean check.
     *
     * PARAMETERS:
     *     address - Address string to validate
     *
     * RETURNS:
     *     Result<InetAddress> - Validated address or validation error
     */
    fun validateIPAddress(address: String): Result<InetAddress> {
        return try {
            if (address.isBlank()) {
                return Result.failure(IllegalArgumentException("IP address cannot be blank"))
            }

            val inetAddress = InetAddress.getByName(address)
            
            // Ensure it's actually an IP address, not a resolved hostname
            if (!isValidIPAddress(address)) {
                return Result.failure(IllegalArgumentException("Invalid IP address format: $address"))
            }

            Result.success(inetAddress)

        } catch (e: Exception) {
            Result.failure(IllegalArgumentException("Invalid IP address: $address", e))
        }
    }

    /**
     * NAME: createNetworkEndpoint
     *
     * DESCRIPTION:
     *     Creates NetworkEndpoint from hostname and port.
     *     Resolves hostname if necessary and validates port range.
     *
     * PARAMETERS:
     *     hostname - Hostname or IP address
     *     port - Port number
     *
     * RETURNS:
     *     Result<NetworkEndpoint> - Created endpoint or validation error
     */
    suspend fun createNetworkEndpoint(hostname: String, port: Int): Result<NetworkEndpoint> {
        return try {
            // Validate port range
            if (port < 1 || port > 65535) {
                return Result.failure(IllegalArgumentException("Invalid port number: $port"))
            }

            // Resolve hostname to IP address
            val addressResult = resolveAddress(hostname)
            
            addressResult.fold(
                onSuccess = { address ->
                    val endpoint = NetworkEndpoint(
                        address = address,
                        port = port,
                        hostname = if (hostname != address.hostAddress) hostname else null
                    )
                    Result.success(endpoint)
                },
                onFailure = { error ->
                    Result.failure(error)
                }
            )

        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * NAME: measureLatency
     *
     * DESCRIPTION:
     *     Measures network latency to specified endpoint.
     *     Sends small UDP packet and measures round-trip time.
     *
     * PARAMETERS:
     *     endpoint - Target endpoint
     *     timeoutMs - Timeout for latency measurement
     *
     * RETURNS:
     *     Result<Long> - Latency in milliseconds or measurement failure
     */
    suspend fun measureLatency(
        endpoint: NetworkEndpoint, 
        timeoutMs: Long = 3000L
    ): Result<Long> = withContext(Dispatchers.IO) {
        try {
            val startTime = System.currentTimeMillis()
            
            // Create temporary UDP socket for ping
            val socket = DatagramSocket()
            socket.soTimeout = timeoutMs.toInt()
            
            try {
                // Send small ping packet
                val pingData = ByteArray(8) { 0x00 }
                val packet = DatagramPacket(
                    pingData, 
                    pingData.size, 
                    endpoint.address, 
                    endpoint.port
                )
                
                socket.send(packet)
                
                // Try to receive response (may timeout)
                val responseBuffer = ByteArray(1024)
                val responsePacket = DatagramPacket(responseBuffer, responseBuffer.size)
                
                try {
                    socket.receive(responsePacket)
                    val latency = System.currentTimeMillis() - startTime
                    
                    logDebug("Latency measurement successful", mapOf(
                        "endpoint" to endpoint.toString(),
                        "latency_ms" to latency
                    ))
                    
                    Result.success(latency)
                } catch (e: SocketTimeoutException) {
                    // No response received, but packet was sent
                    val latency = System.currentTimeMillis() - startTime
                    Result.success(latency)
                }
                
            } finally {
                socket.close()
            }

        } catch (e: Exception) {
            logError("Latency measurement failed", mapOf(
                "endpoint" to endpoint.toString(),
                "error" to (e.message ?: "unknown")
            ), e)
            Result.failure(e)
        }
    }

    /**
     * NAME: getLocalIPAddress
     *
     * DESCRIPTION:
     *     Gets local IP address of the device.
     *     Returns first non-loopback IPv4 address found.
     *
     * RETURNS:
     *     Result<String> - Local IP address or failure result
     */
    fun getLocalIPAddress(): Result<String> {
        return try {
            val interfaces = java.net.NetworkInterface.getNetworkInterfaces()

            for (networkInterface in interfaces) {
                if (networkInterface.isLoopback || !networkInterface.isUp) {
                    continue
                }

                for (address in networkInterface.inetAddresses) {
                    if (address is Inet4Address && !address.isLoopbackAddress) {
                        return Result.success(address.hostAddress)
                    }
                }
            }

            Result.failure(Exception("No local IP address found"))

        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * NAME: getPhysicalInterfaceInfo
     *
     * DESCRIPTION:
     *     Gets physical network interface information (interface name and IP).
     *     Uses ConnectivityManager for accurate mobile data interface detection.
     *     Returns the default route interface information, similar to iOS implementation.
     *
     * PARAMETERS:
     *     context - Android context for accessing system services
     *
     * RETURNS:
     *     Pair<String, String> - Interface name and IP address
     */
    fun getPhysicalInterfaceInfo(context: Context): Pair<String, String> {
        return try {
            // Method 1: Try ConnectivityManager first (more accurate for mobile data)
            val connectivityResult = getPhysicalInterfaceViaConnectivityManager(context)
            if (connectivityResult.first != "unknown" && connectivityResult.second != "unknown") {
                logDebug("Found interface via ConnectivityManager", mapOf(
                    "interface_name" to connectivityResult.first,
                    "ip_address" to connectivityResult.second,
                    "method" to "connectivity_manager"
                ))
                return connectivityResult
            }

            // Method 2: Fallback to NetworkInterface enumeration
            logDebug("ConnectivityManager failed, trying NetworkInterface enumeration")
            val networkInterfaceResult = getPhysicalInterfaceViaNetworkInterface()
            if (networkInterfaceResult.first != "unknown" && networkInterfaceResult.second != "unknown") {
                logDebug("Found interface via NetworkInterface", mapOf(
                    "interface_name" to networkInterfaceResult.first,
                    "ip_address" to networkInterfaceResult.second,
                    "method" to "network_interface"
                ))
                return networkInterfaceResult
            }

            logWarn("No physical interface found using any method, using defaults")
            Pair("unknown", "unknown")

        } catch (e: Exception) {
            logError("Failed to get physical interface info", e)
            Pair("unknown", "unknown")
        }
    }

    /**
     * NAME: getPhysicalInterfaceInfo
     *
     * DESCRIPTION:
     *     Overloaded method for backward compatibility.
     *     Gets physical network interface information without context parameter.
     *     Falls back to NetworkInterface enumeration only.
     *
     * RETURNS:
     *     Pair<String, String> - Interface name and IP address
     */
    fun getPhysicalInterfaceInfo(): Pair<String, String> {
        return try {
            logDebug("Using NetworkInterface enumeration (no context provided)")
            val networkInterfaceResult = getPhysicalInterfaceViaNetworkInterface()
            if (networkInterfaceResult.first != "unknown" && networkInterfaceResult.second != "unknown") {
                logDebug("Found interface via NetworkInterface", mapOf(
                    "interface_name" to networkInterfaceResult.first,
                    "ip_address" to networkInterfaceResult.second,
                    "method" to "network_interface_fallback"
                ))
                return networkInterfaceResult
            }

            logWarn("No physical interface found using NetworkInterface enumeration")
            Pair("unknown", "unknown")

        } catch (e: Exception) {
            logError("Failed to get physical interface info", e)
            Pair("unknown", "unknown")
        }
    }

    /**
     * NAME: getPhysicalInterfaceViaConnectivityManager
     *
     * DESCRIPTION:
     *     Gets interface info using ConnectivityManager and LinkProperties.
     *     This method is more accurate for mobile data connections (4G/5G).
     *     Filters out VPN interfaces to return only physical network interfaces.
     *
     * PARAMETERS:
     *     context - Android context for accessing system services
     *
     * RETURNS:
     *     Pair<String, String> - Interface name and IP address
     */
    private fun getPhysicalInterfaceViaConnectivityManager(context: Context): Pair<String, String> {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

            // Get active network
            val activeNetwork = connectivityManager.activeNetwork
            if (activeNetwork == null) {
                logDebug("No active network found")
                return Pair("unknown", "unknown")
            }

            // Get network capabilities to check if this is a VPN network
            val capabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
            if (capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_VPN) == true) {
                logDebug("Active network is VPN, trying to get underlying physical network")

                // Try to get underlying networks when VPN is active
                // This requires API level 28+ and proper VPN configuration
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                    try {
                        // Get all networks and find non-VPN networks
                        val allNetworks = connectivityManager.allNetworks
                        for (network in allNetworks) {
                            val networkCaps = connectivityManager.getNetworkCapabilities(network)
                            if (networkCaps != null && !networkCaps.hasTransport(NetworkCapabilities.TRANSPORT_VPN)) {
                                // Found a non-VPN network, try to get its interface info
                                val linkProps = connectivityManager.getLinkProperties(network)
                                val interfaceName = linkProps?.interfaceName
                                if (!interfaceName.isNullOrEmpty() && !isVPNInterface(interfaceName)) {
                                    val interfaceInfo = extractInterfaceInfo(linkProps, networkCaps, interfaceName)
                                    if (interfaceInfo.first != "unknown") {
                                        logDebug("Found underlying physical network", mapOf(
                                            "interface_name" to interfaceInfo.first,
                                            "ip_address" to interfaceInfo.second,
                                            "method" to "underlying_network"
                                        ))
                                        return interfaceInfo
                                    }
                                }
                            }
                        }
                    } catch (e: Exception) {
                        logWarn("Failed to get underlying network info", mapOf("error" to (e.message ?: "unknown")))
                    }
                }

                logDebug("Could not find underlying physical network, falling back to NetworkInterface enumeration")
                return Pair("unknown", "unknown")
            }

            // Get link properties for interface name
            val linkProperties = connectivityManager.getLinkProperties(activeNetwork)
            val interfaceName = linkProperties?.interfaceName
            if (interfaceName.isNullOrEmpty()) {
                logDebug("No interface name found in LinkProperties")
                return Pair("unknown", "unknown")
            }

            // Check if this is a VPN interface (additional safety check)
            if (isVPNInterface(interfaceName)) {
                logDebug("Interface is VPN interface, skipping", mapOf("interface_name" to interfaceName))
                return Pair("unknown", "unknown")
            }

            // Extract interface information
            val interfaceInfo = extractInterfaceInfo(linkProperties, capabilities, interfaceName)
            return interfaceInfo

        } catch (e: Exception) {
            logError("Failed to get interface info via ConnectivityManager", e)
            Pair("unknown", "unknown")
        }
    }

    /**
     * NAME: isVPNInterface
     *
     * DESCRIPTION:
     *     Checks if the given interface name represents a VPN interface.
     *
     * PARAMETERS:
     *     interfaceName - Network interface name to check
     *
     * RETURNS:
     *     Boolean - true if interface is a VPN interface
     */
    private fun isVPNInterface(interfaceName: String): Boolean {
        return interfaceName.startsWith("tun") ||
               interfaceName.startsWith("tap") ||
               interfaceName.startsWith("ppp") ||
               interfaceName.startsWith("vpn")
    }

    /**
     * NAME: extractInterfaceInfo
     *
     * DESCRIPTION:
     *     Extracts interface information from LinkProperties and NetworkCapabilities.
     *
     * PARAMETERS:
     *     linkProperties - Link properties containing interface details
     *     capabilities - Network capabilities for connection type detection
     *     interfaceName - Interface name
     *
     * RETURNS:
     *     Pair<String, String> - Interface name and IP address
     */
    private fun extractInterfaceInfo(
        linkProperties: LinkProperties,
        capabilities: NetworkCapabilities?,
        interfaceName: String
    ): Pair<String, String> {
        // Get network type for logging
        val networkType = when {
            capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true -> "WiFi"
            capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true -> "Mobile"
            capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) == true -> "Ethernet"
            else -> "Unknown"
        }

        // Get IP address from link addresses
        val linkAddresses = linkProperties.linkAddresses
        for (linkAddress in linkAddresses) {
            val address = linkAddress.address
            if (address is Inet4Address && !address.isLoopbackAddress && !address.isLinkLocalAddress) {
                logDebug("Found interface via ConnectivityManager", mapOf(
                    "interface_name" to interfaceName,
                    "ip_address" to address.hostAddress,
                    "network_type" to networkType,
                    "prefix_length" to linkAddress.prefixLength
                ))
                return Pair(interfaceName, address.hostAddress)
            }
        }

        logDebug("No valid IPv4 address found in LinkProperties for interface", mapOf(
            "interface_name" to interfaceName,
            "network_type" to networkType
        ))
        return Pair("unknown", "unknown")
    }

    /**
     * NAME: getLocalSubnetInfo
     *
     * DESCRIPTION:
     *     Gets the local subnet information (network address and CIDR) for the current device.
     *     Uses ConnectivityManager to get accurate network interface information including
     *     IP address and prefix length, then calculates the network address.
     *
     * PARAMETERS:
     *     context - Android context for accessing system services
     *
     * RETURNS:
     *     Pair<String, Int>? - Network address and prefix length, or null if not available
     *
     * EXAMPLE:
     *     Device IP: *************/24 -> Returns ("***********", 24)
     *     Device IP: *********/16 -> Returns ("10.0.0.0", 16)
     */
    fun getLocalSubnetInfo(context: Context): Pair<String, Int>? {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

            // Get active network
            val activeNetwork = connectivityManager.activeNetwork
            if (activeNetwork == null) {
                logDebug("No active network found for subnet calculation")
                return null
            }

            // Get link properties
            val linkProperties = connectivityManager.getLinkProperties(activeNetwork)
            if (linkProperties == null) {
                logDebug("No link properties found for subnet calculation")
                return null
            }

            // Find IPv4 address with prefix length
            val linkAddresses = linkProperties.linkAddresses
            for (linkAddress in linkAddresses) {
                val address = linkAddress.address
                if (address is Inet4Address && !address.isLoopbackAddress && !address.isLinkLocalAddress) {
                    val prefixLength = linkAddress.prefixLength
                    val networkAddress = calculateNetworkAddress(address, prefixLength)

                    logDebug("Calculated local subnet", mapOf(
                        "device_ip" to address.hostAddress,
                        "prefix_length" to prefixLength,
                        "network_address" to networkAddress,
                        "subnet_cidr" to "$networkAddress/$prefixLength"
                    ))

                    return Pair(networkAddress, prefixLength)
                }
            }

            logDebug("No valid IPv4 address found for subnet calculation")
            null

        } catch (e: Exception) {
            logError("Failed to get local subnet info", e)
            null
        }
    }

    /**
     * NAME: calculateNetworkAddress
     *
     * DESCRIPTION:
     *     Calculates the network address from an IP address and prefix length.
     *     Applies the subnet mask to get the network portion of the address.
     *
     * PARAMETERS:
     *     ipAddress - The IP address (e.g., *************)
     *     prefixLength - The CIDR prefix length (e.g., 24)
     *
     * RETURNS:
     *     String - The network address (e.g., "***********")
     *
     * ALGORITHM:
     *     1. Create subnet mask from prefix length
     *     2. Apply bitwise AND between IP and mask
     *     3. Convert result back to dotted decimal notation
     */
    private fun calculateNetworkAddress(ipAddress: Inet4Address, prefixLength: Int): String {
        return try {
            // Get IP address as 32-bit integer
            val ipBytes = ipAddress.address
            var ipInt = 0
            for (i in ipBytes.indices) {
                ipInt = (ipInt shl 8) or (ipBytes[i].toInt() and 0xFF)
            }

            // Create subnet mask from prefix length
            val mask = if (prefixLength == 0) 0 else (-1 shl (32 - prefixLength))

            // Apply mask to get network address
            val networkInt = ipInt and mask

            // Convert back to dotted decimal notation
            val networkAddress = String.format(
                "%d.%d.%d.%d",
                (networkInt ushr 24) and 0xFF,
                (networkInt ushr 16) and 0xFF,
                (networkInt ushr 8) and 0xFF,
                networkInt and 0xFF
            )

            logDebug("Network address calculation", mapOf(
                "ip_address" to ipAddress.hostAddress,
                "prefix_length" to prefixLength,
                "network_address" to networkAddress,
                "ip_int" to ipInt.toString(16),
                "mask_int" to mask.toString(16),
                "network_int" to networkInt.toString(16)
            ))

            networkAddress

        } catch (e: Exception) {
            logError("Failed to calculate network address", e)
            ipAddress.hostAddress // Fallback to original IP
        }
    }

    /**
     * NAME: getPhysicalInterfaceViaNetworkInterface
     *
     * DESCRIPTION:
     *     Gets interface info using NetworkInterface enumeration.
     *     Fallback method when ConnectivityManager fails.
     *
     * RETURNS:
     *     Pair<String, String> - Interface name and IP address
     */
    private fun getPhysicalInterfaceViaNetworkInterface(): Pair<String, String> {
        return try {
            val interfaces = java.net.NetworkInterface.getNetworkInterfaces()

            // Enhanced priority order with more mobile data interface patterns
            val interfacePriority = listOf(
                "wlan",     // WiFi interfaces
                "eth",      // Ethernet interfaces
                "rmnet",    // Qualcomm mobile data interfaces
                "ccmni",    // MediaTek mobile data interfaces
                "pdp_ip",   // Some mobile data interfaces
                "v4-",      // Some carrier mobile data interfaces
                "r_rmnet",  // Some Qualcomm variants
                "en"        // Generic network interfaces
            )

            // First pass: try priority interfaces
            for (priority in interfacePriority) {
                for (networkInterface in interfaces) {
                    if (networkInterface.isLoopback || !networkInterface.isUp) {
                        continue
                    }

                    val interfaceName = networkInterface.name
                    if (interfaceName.startsWith(priority)) {
                        for (address in networkInterface.inetAddresses) {
                            if (address is Inet4Address && !address.isLoopbackAddress && !address.isLinkLocalAddress) {
                                logDebug("Found priority interface", mapOf(
                                    "interface_name" to interfaceName,
                                    "ip_address" to address.hostAddress,
                                    "priority" to priority
                                ))
                                return Pair(interfaceName, address.hostAddress)
                            }
                        }
                    }
                }
            }

            // Second pass: any active non-loopback interface
            for (networkInterface in interfaces) {
                if (networkInterface.isLoopback || !networkInterface.isUp) {
                    continue
                }

                val interfaceName = networkInterface.name
                // Skip VPN and virtual interfaces
                if (interfaceName.startsWith("tun") || interfaceName.startsWith("tap") ||
                    interfaceName.startsWith("lo") || interfaceName.startsWith("dummy") ||
                    interfaceName.startsWith("sit") || interfaceName.startsWith("ip6tnl")) {
                    continue
                }

                for (address in networkInterface.inetAddresses) {
                    if (address is Inet4Address && !address.isLoopbackAddress && !address.isLinkLocalAddress) {
                        logDebug("Found fallback interface", mapOf(
                            "interface_name" to interfaceName,
                            "ip_address" to address.hostAddress
                        ))
                        return Pair(interfaceName, address.hostAddress)
                    }
                }
            }

            logDebug("No valid interface found via NetworkInterface enumeration")
            Pair("unknown", "unknown")

        } catch (e: Exception) {
            logError("Failed to get interface info via NetworkInterface", e)
            Pair("unknown", "unknown")
        }
    }
}
