#!/bin/bash
echo "Building ItForce VPN Client Frontend..."

# 设置项目根目录
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"

# 设置版本信息
VERSION_FILE="$PROJECT_ROOT/VERSION"
if [ -f "$VERSION_FILE" ]; then
    VERSION=$(cat "$VERSION_FILE" | tr -d '\n\r')
    echo "[INFO] Reading version from file: $VERSION"
else
    VERSION="1.0.2"
    echo "[WARNING] Version file not found, using default: $VERSION"
fi

TIMESTAMP=$(date +%Y%m%d%H%M%S)

# 获取Git提交信息
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 生成最终版本号
BUILD_ID="$VERSION-$GIT_COMMIT-$TIMESTAMP"
echo "Version: $BUILD_ID"

# 更新 pubspec.yaml 中的版本号
PUBSPEC_FILE="$PROJECT_ROOT/ui/flutter/pubspec.yaml"
if [ -f "$PUBSPEC_FILE" ]; then
    echo "[INFO] Updating pubspec.yaml version to $VERSION"
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/^version:[[:space:]]*[^[:space:]]*/version: $VERSION+1/" "$PUBSPEC_FILE"
        sed -i '' "s/msix_version:[[:space:]]*[^[:space:]]*/msix_version: $VERSION.0/" "$PUBSPEC_FILE"
    else
        # Linux
        sed -i "s/^version:[[:space:]]*[^[:space:]]*/version: $VERSION+1/" "$PUBSPEC_FILE"
        sed -i "s/msix_version:[[:space:]]*[^[:space:]]*/msix_version: $VERSION.0/" "$PUBSPEC_FILE"
    fi
    echo "[SUCCESS] pubspec.yaml version updated"
else
    echo "[WARNING] pubspec.yaml not found: $PUBSPEC_FILE"
fi

# 检查 Flutter 是否已安装
if ! command -v flutter &> /dev/null; then
    echo "Error: Flutter not found in PATH"
    echo "Please install Flutter and add it to your PATH"
    exit 1
fi

# 创建输出目录
mkdir -p build/frontend

# 切换到 Flutter 项目目录
cd ui/flutter

# 获取 Flutter 依赖
echo "Getting Flutter dependencies..."
flutter pub get

if [ $? -ne 0 ]; then
    echo "Error: Failed to get Flutter dependencies"
    exit 1
fi

# 生成应用图标
echo "Generating app icons..."
flutter pub run flutter_launcher_icons:main

if [ $? -ne 0 ]; then
    echo "Warning: Failed to generate app icons"
fi

# 构建 Flutter Windows 应用
echo "Building Flutter Windows application..."
flutter build windows --release

if [ $? -ne 0 ]; then
    echo "Error: Failed to build Flutter application"
    exit 1
fi

# 复制构建的文件到输出目录
echo "Copying Flutter build to output directory..."
if [ -d "build/windows/x64/runner/Release" ]; then
    cp -R build/windows/x64/runner/Release/* ../../build/frontend/
elif [ -d "build/windows/runner/Release" ]; then
    cp -R build/windows/runner/Release/* ../../build/frontend/
else
    echo "Error: Flutter build output directory not found"
    exit 1
fi

# 创建版本信息文件
echo "Creating version file..."
echo "$BUILD_ID" > ../../build/frontend/version.txt

# 注意：Flutter已自动将assets打包到data/flutter_assets中，不需要额外复制
# 这样可以避免重复的assets目录，减少安装包体积
echo "Skipping additional assets copy - Flutter handles assets automatically"

# 验证字体文件是否被Flutter正确打包
echo "Verifying font files in Flutter assets..."
FLUTTER_ASSETS_FONTS_PATH=""
if [ -d "build/windows/x64/runner/Release/data/flutter_assets/fonts" ]; then
    FLUTTER_ASSETS_FONTS_PATH="build/windows/x64/runner/Release/data/flutter_assets/fonts"
elif [ -d "build/windows/runner/Release/data/flutter_assets/fonts" ]; then
    FLUTTER_ASSETS_FONTS_PATH="build/windows/runner/Release/data/flutter_assets/fonts"
fi

if [ -n "$FLUTTER_ASSETS_FONTS_PATH" ]; then
    if [ -f "$FLUTTER_ASSETS_FONTS_PATH/SourceHanSansCN-Regular.otf" ]; then
        echo "✓ SourceHanSansCN fonts found in Flutter assets"
    else
        echo "⚠ SourceHanSansCN fonts not found in Flutter assets"
    fi
    if [ -f "$FLUTTER_ASSETS_FONTS_PATH/Roboto-Regular.ttf" ]; then
        echo "✓ Roboto fonts found in Flutter assets"
    else
        echo "⚠ Roboto fonts not found in Flutter assets"
    fi
else
    echo "⚠ Flutter fonts directory not found"
fi

# 验证Flutter自动生成的图标文件
echo "Verifying Flutter-generated icon files..."
FLUTTER_ASSETS_PATH=""
if [ -d "build/windows/x64/runner/Release/data/flutter_assets/assets/icons" ]; then
    FLUTTER_ASSETS_PATH="build/windows/x64/runner/Release/data/flutter_assets/assets/icons"
elif [ -d "build/windows/runner/Release/data/flutter_assets/assets/icons" ]; then
    FLUTTER_ASSETS_PATH="build/windows/runner/Release/data/flutter_assets/assets/icons"
fi

if [ -n "$FLUTTER_ASSETS_PATH" ]; then
    if [ -f "$FLUTTER_ASSETS_PATH/app_icon.ico" ]; then
        echo "✓ ICO icon found in Flutter assets"
    else
        echo "⚠ ICO icon not found in Flutter assets"
    fi
    if [ -f "$FLUTTER_ASSETS_PATH/app_icon.png" ]; then
        echo "✓ PNG icon found in Flutter assets"
    else
        echo "⚠ PNG icon not found in Flutter assets"
    fi
else
    echo "⚠ Flutter assets directory not found"
fi

# 复制国际化文件
if [ -d "l10n" ]; then
    echo "Copying localization files..."
    mkdir -p ../../build/frontend/l10n
    cp -R l10n/* ../../build/frontend/l10n/
fi

echo "Frontend build completed successfully!"

# 返回到原始目录
cd ../..
