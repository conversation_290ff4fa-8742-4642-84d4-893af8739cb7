@echo off
setlocal enabledelayedexpansion
echo Building Panabit Client Frontend...

REM 设置项目根目录
set PROJECT_ROOT=%~dp0..

REM 设置版本信息
set VERSION_FILE=%PROJECT_ROOT%\VERSION
if exist "%VERSION_FILE%" (
    set /p VERSION=<"%VERSION_FILE%"
    echo [INFO] Reading version from file: !VERSION!
) else (
    set VERSION=1.0.2
    echo [WARNING] Version file not found, using default: !VERSION!
)

REM 使用PowerShell获取标准格式的日期时间 (年月日时分秒)
for /f "delims=" %%a in ('powershell -Command "Get-Date -Format 'yyyyMMddHHmmss'"') do set TIMESTAMP=%%a

REM 获取Git提交信息
for /f "tokens=*" %%a in ('git rev-parse --short HEAD 2^>nul') do set GIT_COMMIT=%%a
if "%GIT_COMMIT%"=="" set GIT_COMMIT=unknown

REM 生成最终版本号
set BUILD_ID=!VERSION!-%GIT_COMMIT%-%TIMESTAMP%
echo Version: !BUILD_ID!

REM 更新 pubspec.yaml 中的版本号
set PUBSPEC_FILE=%PROJECT_ROOT%\ui\flutter\pubspec.yaml
if exist "%PUBSPEC_FILE%" (
    echo [INFO] Updating pubspec.yaml version to !VERSION!
    powershell -ExecutionPolicy Bypass -File "%PROJECT_ROOT%\scripts\update-pubspec-version.ps1" -Version "!VERSION!" -PubspecPath "%PUBSPEC_FILE%"
    if errorlevel 1 (
        echo [ERROR] Failed to update pubspec.yaml
        exit /b 1
    )
) else (
    echo [WARNING] pubspec.yaml not found: %PUBSPEC_FILE%
)

REM 检查 Flutter 是否已安装
where flutter >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: Flutter not found in PATH
    echo Please install Flutter and add it to your PATH
    exit /b 1
)

REM 创建输出目录
if not exist build\frontend mkdir build\frontend

REM 切换到 Flutter 项目目录
cd ui\flutter

REM 获取 Flutter 依赖
echo Getting Flutter dependencies...
call flutter pub get

if %ERRORLEVEL% neq 0 (
    echo Error: Failed to get Flutter dependencies
    exit /b 1
)

REM 生成应用图标
echo Generating app icons...
call flutter pub run flutter_launcher_icons:main

if %ERRORLEVEL% neq 0 (
    echo Warning: Failed to generate app icons
)

REM 构建 Flutter Windows 应用
echo Building Flutter Windows application...
call flutter build windows --release

if %ERRORLEVEL% neq 0 (
    echo Error: Failed to build Flutter application
    exit /b 1
)

REM 复制构建的文件到输出目录
echo Copying Flutter build to output directory...
if exist build\windows\x64\runner\Release (
    xcopy /E /I /Y build\windows\x64\runner\Release\* ..\..\build\frontend\
) else if exist build\windows\runner\Release (
    xcopy /E /I /Y build\windows\runner\Release\* ..\..\build\frontend\
) else (
    echo Error: Flutter build output directory not found
    exit /b 1
)

REM 创建版本信息文件
echo Creating version file...
echo !BUILD_ID! > ..\..\build\frontend\version.txt

REM 注意：Flutter已自动将assets打包到data/flutter_assets中，不需要额外复制
REM 这样可以避免重复的assets目录，减少安装包体积
echo Skipping additional assets copy - Flutter handles assets automatically

REM 验证字体文件是否被Flutter正确打包
echo Verifying font files in Flutter assets...
set FLUTTER_ASSETS_FONTS_PATH=
if exist build\windows\x64\runner\Release\data\flutter_assets\fonts (
    set FLUTTER_ASSETS_FONTS_PATH=build\windows\x64\runner\Release\data\flutter_assets\fonts
) else if exist build\windows\runner\Release\data\flutter_assets\fonts (
    set FLUTTER_ASSETS_FONTS_PATH=build\windows\runner\Release\data\flutter_assets\fonts
)

if defined FLUTTER_ASSETS_FONTS_PATH (
    if exist %FLUTTER_ASSETS_FONTS_PATH%\SourceHanSansCN-Regular.otf (
        echo ✓ SourceHanSansCN fonts found in Flutter assets
    ) else (
        echo ⚠ SourceHanSansCN fonts not found in Flutter assets
    )
    if exist %FLUTTER_ASSETS_FONTS_PATH%\Roboto-Regular.ttf (
        echo ✓ Roboto fonts found in Flutter assets
    ) else (
        echo ⚠ Roboto fonts not found in Flutter assets
    )
) else (
    echo ⚠ Flutter fonts directory not found
)

REM 验证Flutter自动生成的图标文件
echo Verifying Flutter-generated icon files...
set FLUTTER_ASSETS_PATH=
if exist build\windows\x64\runner\Release\data\flutter_assets\assets\icons (
    set FLUTTER_ASSETS_PATH=build\windows\x64\runner\Release\data\flutter_assets\assets\icons
) else if exist build\windows\runner\Release\data\flutter_assets\assets\icons (
    set FLUTTER_ASSETS_PATH=build\windows\runner\Release\data\flutter_assets\assets\icons
)

if defined FLUTTER_ASSETS_PATH (
    if exist %FLUTTER_ASSETS_PATH%\app_icon.ico (
        echo ✓ ICO icon found in Flutter assets
    ) else (
        echo ⚠ ICO icon not found in Flutter assets
    )
    if exist %FLUTTER_ASSETS_PATH%\app_icon.png (
        echo ✓ PNG icon found in Flutter assets
    ) else (
        echo ⚠ PNG icon not found in Flutter assets
    )
) else (
    echo ⚠ Flutter assets directory not found
)

REM 复制国际化文件
if exist l10n (
    echo Copying localization files...
    if not exist ..\..\build\frontend\l10n mkdir ..\..\build\frontend\l10n
    xcopy /E /I /Y l10n\* ..\..\build\frontend\l10n\
)

echo Frontend build completed successfully!

REM 返回到原始目录
cd ..\..
